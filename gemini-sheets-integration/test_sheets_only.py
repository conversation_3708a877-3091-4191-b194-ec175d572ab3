#!/usr/bin/env python3
"""
Test Google Sheets integration without Gemini
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import GeminiSheetsIntegration, format_data_for_analysis

def test_sheets_integration():
    """Test only the Google Sheets part of the integration"""
    print("🧪 Testing Google Sheets Integration (without Gemini)")
    print("=" * 60)
    
    # Test spreadsheet ID
    SPREADSHEET_ID = "1BBhFEyhv0f3IJl4UC3ou5J9dxvN32wcSCjEASAsP5sM"
    
    try:
        # Create a mock integration that skips Gemini setup
        class SheetsOnlyIntegration:
            def __init__(self):
                self.sheets_service = None
                self.setup_google_sheets()
            
            def setup_google_sheets(self):
                from google.auth import default
                from googleapiclient.discovery import build
                
                credentials, project = default(scopes=[
                    'https://www.googleapis.com/auth/spreadsheets.readonly',
                    'https://www.googleapis.com/auth/drive.readonly'
                ])
                
                self.sheets_service = build('sheets', 'v4', credentials=credentials)
                print(f"✅ Google Sheets API initialized for project: {project}")
            
            def get_spreadsheet_data(self, spreadsheet_id, sheet_name=None, range_name=None):
                from googleapiclient.errors import HttpError
                
                try:
                    # Get spreadsheet metadata
                    spreadsheet = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
                    title = spreadsheet.get('properties', {}).get('title', 'Unknown')
                    
                    # If no sheet specified, use the first one
                    if not sheet_name:
                        sheets = spreadsheet.get('sheets', [])
                        if sheets:
                            sheet_name = sheets[0].get('properties', {}).get('title', 'Sheet1')
                        else:
                            raise ValueError("No sheets found in spreadsheet")
                    
                    # Build range string
                    if range_name:
                        full_range = f"{sheet_name}!{range_name}"
                    else:
                        full_range = sheet_name
                    
                    # Get the data
                    result = self.sheets_service.spreadsheets().values().get(
                        spreadsheetId=spreadsheet_id,
                        range=full_range
                    ).execute()
                    
                    values = result.get('values', [])
                    
                    return {
                        'spreadsheet_title': title,
                        'sheet_name': sheet_name,
                        'range': full_range,
                        'data': values,
                        'row_count': len(values),
                        'column_count': len(values[0]) if values else 0
                    }
                    
                except HttpError as error:
                    print(f"❌ HTTP Error accessing spreadsheet: {error}")
                    raise
                except Exception as error:
                    print(f"❌ Error fetching spreadsheet data: {error}")
                    raise
        
        # Initialize the sheets-only integration
        integration = SheetsOnlyIntegration()
        
        # Fetch spreadsheet data
        print(f"\n📊 Fetching data from spreadsheet...")
        data = integration.get_spreadsheet_data(SPREADSHEET_ID)
        
        print(f"✅ Successfully loaded: {data['spreadsheet_title']}")
        print(f"📋 Sheet: {data['sheet_name']}")
        print(f"📏 Dimensions: {data['row_count']} rows × {data['column_count']} columns")
        
        # Display sample data
        print(f"\n📋 Sample data (first 5 rows):")
        for i, row in enumerate(data['data'][:5]):
            print(f"  Row {i+1}: {row}")
        
        # Test data formatting
        print(f"\n📝 Testing data formatting for AI analysis...")
        formatted_data = format_data_for_analysis(data)
        print(f"✅ Data formatted successfully ({len(formatted_data)} characters)")
        
        # Show a preview of formatted data
        print(f"\n📋 Formatted data preview (first 500 characters):")
        print(formatted_data[:500] + "..." if len(formatted_data) > 500 else formatted_data)
        
        print(f"\n🎉 Google Sheets integration test completed successfully!")
        print(f"📊 Ready for Gemini AI integration!")
        
        return True
        
    except Exception as error:
        print(f"❌ Error in test: {error}")
        return False

if __name__ == "__main__":
    success = test_sheets_integration()
    if success:
        print(f"\n✅ All tests passed!")
        print(f"💡 To test with Gemini AI, set GEMINI_API_KEY and run: uv run main.py")
    else:
        print(f"\n❌ Tests failed!")
        sys.exit(1)
