# Gemini-Google Sheets Integration

A powerful integration that enables natural language queries against Google Sheets data using Google's Gemini AI. This project combines the MCP Google Sheets server with Gemini AI to provide intelligent data analysis capabilities.

## Features

- 🔗 **Seamless Google Sheets Integration**: Connect to any Google Spreadsheet using Application Default Credentials
- 🤖 **Natural Language Queries**: Ask questions about your data in plain English
- 📊 **Intelligent Data Analysis**: Get insights, calculations, and summaries powered by Gemini AI
- 🔐 **Secure Authentication**: Uses Google Cloud Application Default Credentials
- 💬 **Interactive Mode**: Real-time question-and-answer interface
- 📋 **Sample Queries**: Pre-built examples to get you started

## Prerequisites

1. **Google Cloud Setup**:
   - Google Cloud project with Sheets API and Drive API enabled
   - Application Default Credentials configured (`gcloud auth application-default login`)

2. **Gemini API Key**:
   - Get a free API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Set as environment variable: `export GEMINI_API_KEY="your-api-key"`

## Installation

1. **Clone and setup**:
   ```bash
   cd gemini-sheets-integration
   uv sync
   ```

2. **Set environment variables**:
   ```bash
   export GEMINI_API_KEY="your-gemini-api-key"
   ```

## Usage

### Basic Usage

```bash
uv run main.py
```

The script will:
1. Connect to Google Sheets using your ADC credentials
2. Load data from the test spreadsheet
3. Run sample queries using Gemini AI
4. Enter interactive mode for custom queries

### Example Queries

- "What is the average salary in the Engineering department?"
- "How many employees are there in each city?"
- "Who is the youngest employee and what department do they work in?"
- "What is the salary range across all departments?"
- "Show me the top 3 highest paid employees"
- "Which city has the most employees?"

### Programmatic Usage

```python
from main import GeminiSheetsIntegration

# Initialize the integration
integration = GeminiSheetsIntegration(gemini_api_key="your-key")

# Fetch spreadsheet data
data = integration.get_spreadsheet_data("your-spreadsheet-id")

# Query with Gemini
response = query_data_with_gemini(
    integration.gemini_model,
    format_data_for_analysis(data),
    "Your question here"
)
print(response)
```

## Configuration

### Environment Variables

- `GEMINI_API_KEY`: Your Gemini AI API key (required)
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to service account key (optional, uses ADC by default)

### Spreadsheet Access

The integration uses Google Cloud Application Default Credentials, which means:
- It works with any spreadsheet you have access to
- No need to share spreadsheets with service accounts for personal use
- Seamless integration with your existing Google account

## MCP Server Integration

This project works alongside the MCP Google Sheets server. The complete setup includes:

1. **MCP Server**: Handles Google Sheets operations via JSON-RPC
2. **Claude Desktop**: Provides the MCP client interface
3. **Gemini Integration**: Adds AI-powered data analysis capabilities

### Claude Desktop Configuration

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "google-sheets": {
      "command": "/path/to/uvx",
      "args": ["mcp-google-sheets@latest"],
      "env": {}
    }
  }
}
```

## Sample Data

The integration includes a test spreadsheet with employee data:

| Name | Age | City | Department | Salary |
|------|-----|------|------------|--------|
| Alice Johnson | 28 | New York | Engineering | 75000 |
| Bob Smith | 34 | San Francisco | Marketing | 65000 |
| Carol Davis | 29 | Chicago | Engineering | 80000 |
| ... | ... | ... | ... | ... |

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   ```bash
   gcloud auth application-default login --scopes=https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/spreadsheets,https://www.googleapis.com/auth/drive
   ```

2. **Gemini API Errors**:
   - Verify your API key is correct
   - Check your API quota and billing settings
   - Ensure you're using a supported region

3. **Spreadsheet Access**:
   - Verify the spreadsheet ID is correct
   - Ensure you have read access to the spreadsheet
   - Check that the sheet name exists

### Debug Mode

Set environment variable for verbose logging:
```bash
export DEBUG=1
uv run main.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Related Projects

- [MCP Google Sheets Server](https://github.com/xing5/mcp-google-sheets)
- [Google Generative AI Python SDK](https://github.com/google/generative-ai-python)
- [Google API Python Client](https://github.com/googleapis/google-api-python-client)