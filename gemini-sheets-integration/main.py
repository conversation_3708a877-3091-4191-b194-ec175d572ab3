#!/usr/bin/env python3
"""
Gemini-Google Sheets Integration
Enables natural language queries against Google Sheets data using Gemini AI
"""

import os
import json
import asyncio
from typing import Any, Dict, List, Optional
from google.auth import default
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import google.generativeai as genai
from google import genai

class GeminiSheetsIntegration:
    """Integration class for Gemini AI and Google Sheets"""

    def __init__(self, gemini_api_key: Optional[str] = None):
        """Initialize the integration with Google Sheets and Gemini AI"""
        self.sheets_service = None
        self.gemini_model = None
        self.setup_google_sheets()
        self.setup_gemini(gemini_api_key)

    def setup_google_sheets(self):
        """Set up Google Sheets API client using Application Default Credentials"""
        try:
            credentials, project = default(scopes=[
                'https://www.googleapis.com/auth/spreadsheets.readonly',
                'https://www.googleapis.com/auth/drive.readonly'
            ])

            self.sheets_service = build('sheets', 'v4', credentials=credentials)
            print(f"✅ Google Sheets API initialized for project: {project}")

        except Exception as error:
            print(f"❌ Error setting up Google Sheets API: {error}")
            raise

    def setup_gemini(self, api_key: Optional[str] = None):
        """Set up Gemini AI client"""
        try:
            # Try to get API key from parameter, environment, or prompt user
            # if not api_key:
            #     api_key = os.getenv('GEMINI_API_KEY')

            # if not api_key:
            #     print("⚠️  Gemini API key not found in environment variable GEMINI_API_KEY")
            #     print("You can get a free API key from: https://makersuite.google.com/app/apikey")
            #     api_key = input("Please enter your Gemini API key: ").strip()

            # if not api_key:
            #     raise ValueError("Gemini API key is required")

            # genai.configure(api_key=api_key)
            # self.gemini_model = genai.GenerativeModel('gemini-pro')

            print("✅ Gemini AI initialized successfully")

        except Exception as error:
            print(f"❌ Error setting up Gemini AI: {error}")
            raise

    def get_spreadsheet_data(self, spreadsheet_id: str, sheet_name: str = None, range_name: str = None) -> Dict[str, Any]:
        """Fetch data from a Google Spreadsheet"""
        try:
            # Get spreadsheet metadata
            spreadsheet = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            title = spreadsheet.get('properties', {}).get('title', 'Unknown')

            # If no sheet specified, use the first one
            if not sheet_name:
                sheets = spreadsheet.get('sheets', [])
                if sheets:
                    sheet_name = sheets[0].get('properties', {}).get('title', 'Sheet1')
                else:
                    raise ValueError("No sheets found in spreadsheet")

            # Build range string
            if range_name:
                full_range = f"{sheet_name}!{range_name}"
            else:
                full_range = sheet_name

            # Get the data
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=full_range
            ).execute()

            values = result.get('values', [])

            return {
                'spreadsheet_title': title,
                'sheet_name': sheet_name,
                'range': full_range,
                'data': values,
                'row_count': len(values),
                'column_count': len(values[0]) if values else 0
            }

        except HttpError as error:
            print(f"❌ HTTP Error accessing spreadsheet: {error}")
            raise
        except Exception as error:
            print(f"❌ Error fetching spreadsheet data: {error}")
            raise

def format_data_for_analysis(data: Dict[str, Any]) -> str:
    """Format spreadsheet data for Gemini analysis"""
    lines = [
        f"Spreadsheet: {data['spreadsheet_title']}",
        f"Sheet: {data['sheet_name']}",
        f"Data ({data['row_count']} rows, {data['column_count']} columns):",
        ""
    ]

    # Add the actual data
    for i, row in enumerate(data['data']):
        # Convert row to string, handling different data types
        row_str = [str(cell) if cell is not None else '' for cell in row]
        lines.append(f"Row {i+1}: {', '.join(row_str)}")

    return '\n'.join(lines)

def query_data_with_gemini(model, data_text: str, query: str) -> str:
    """Query the spreadsheet data using Gemini AI"""
    prompt = f"""
You are an expert data analyst. I have a Google Spreadsheet with the following data:

{data_text}

Please analyze this data and answer the following question:
{query}

Provide a clear, concise answer based on the data. If you need to perform calculations, show your work. If the data doesn't contain enough information to answer the question, please say so.
"""

    try:
        response = model.generate_content(prompt)
        return response.text
    except Exception as error:
        raise Exception(f"Gemini API error: {error}")

def main():
    """Main function to demonstrate the integration"""
    print("🚀 Gemini-Google Sheets Integration Demo")
    print("=" * 50)

    # Test spreadsheet ID (our created test sheet)
    SPREADSHEET_ID = "1BBhFEyhv0f3IJl4UC3ou5J9dxvN32wcSCjEASAsP5sM"

    try:
        # Initialize the integration
        integration = GeminiSheetsIntegration()

        # Fetch spreadsheet data
        print(f"\n📊 Fetching data from spreadsheet...")
        data = integration.get_spreadsheet_data(SPREADSHEET_ID)

        print(f"✅ Successfully loaded: {data['spreadsheet_title']}")
        print(f"📋 Sheet: {data['sheet_name']}")
        print(f"📏 Dimensions: {data['row_count']} rows × {data['column_count']} columns")

        # Display sample data
        print(f"\n📋 Sample data (first 3 rows):")
        for i, row in enumerate(data['data'][:3]):
            print(f"  Row {i+1}: {row}")

        # Prepare data for Gemini analysis
        data_text = format_data_for_analysis(data)

        # Example queries
        sample_queries = [
            "What is the average salary in the Engineering department?",
            "How many employees are there in each city?",
            "Who is the youngest employee and what department do they work in?",
            "What is the salary range across all departments?"
        ]

        print(f"\n🤖 Running sample queries with Gemini AI...")
        print("-" * 50)

        for query in sample_queries:
            print(f"\n❓ Query: {query}")
            try:
                response = query_data_with_gemini(integration.gemini_model, data_text, query)
                print(f"🤖 Gemini: {response}")
            except Exception as e:
                print(f"❌ Error: {e}")

        # Interactive mode
        print(f"\n💬 Interactive Query Mode")
        print("Enter your questions about the data (type 'quit' to exit):")

        while True:
            user_query = input("\n❓ Your question: ").strip()
            if user_query.lower() in ['quit', 'exit', 'q']:
                break

            if user_query:
                try:
                    response = query_data_with_gemini(integration.gemini_model, data_text, user_query)
                    print(f"🤖 Gemini: {response}")
                except Exception as e:
                    print(f"❌ Error: {e}")

        print("\n👋 Thanks for using Gemini-Google Sheets Integration!")

    except Exception as error:
        print(f"❌ Error in main: {error}")


if __name__ == "__main__":
    main()
