#!/usr/bin/env python3
"""
Comprehensive test of MCP Google Sheets server integration
"""

import subprocess
import time
import json
import sys
import os

def test_mcp_server_standalone():
    """Test the MCP server by running it standalone and checking its output"""
    print("🧪 Testing MCP Google Sheets Server")
    print("=" * 50)
    
    try:
        print("🚀 Starting MCP server...")
        
        # Start the MCP server process
        process = subprocess.Popen(
            ["uvx", "mcp-google-sheets@latest"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Give the server time to start and show initial output
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ MCP server started successfully")
            
            # Try to read some output
            try:
                # Use a short timeout to avoid hanging
                stdout, stderr = process.communicate(timeout=2)
                
                if stdout:
                    print("📋 Server stdout:")
                    print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
                
                if stderr:
                    print("📋 Server stderr:")
                    print(stderr[:500] + "..." if len(stderr) > 500 else stderr)
                    
            except subprocess.TimeoutExpired:
                # This is expected - the server should keep running
                print("✅ Server is running (timeout on communicate is expected)")
                
                # Kill the process since it's still running
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                
                print("✅ Server terminated cleanly")
                
        else:
            # Process exited, check why
            stdout, stderr = process.communicate()
            print(f"❌ Server exited with code: {process.returncode}")
            if stdout:
                print("📋 stdout:", stdout)
            if stderr:
                print("📋 stderr:", stderr)
            return False
        
        return True
        
    except Exception as error:
        print(f"❌ Error testing MCP server: {error}")
        return False

def test_uvx_installation():
    """Test that uvx and the MCP package are working"""
    print("\n🔧 Testing uvx and MCP package installation...")
    
    try:
        # Test uvx version
        result = subprocess.run(["uvx", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ uvx version: {result.stdout.strip()}")
        else:
            print(f"❌ uvx version check failed: {result.stderr}")
            return False
        
        # Test if we can resolve the MCP package
        print("📦 Testing MCP package resolution...")
        result = subprocess.run(
            ["uvx", "--dry-run", "mcp-google-sheets@latest", "--help"], 
            capture_output=True, text=True, timeout=30
        )
        
        if result.returncode == 0:
            print("✅ MCP Google Sheets package can be resolved")
        else:
            print(f"❌ MCP package resolution failed: {result.stderr}")
            return False
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ Timeout during uvx testing")
        return False
    except Exception as error:
        print(f"❌ Error testing uvx: {error}")
        return False

def test_google_apis():
    """Test Google APIs access"""
    print("\n☁️ Testing Google APIs access...")
    
    try:
        # Test if we can import and use Google APIs
        from google.auth import default
        from googleapiclient.discovery import build
        
        # Get credentials
        credentials, project = default(scopes=[
            'https://www.googleapis.com/auth/spreadsheets.readonly',
            'https://www.googleapis.com/auth/drive.readonly'
        ])
        
        print(f"✅ Google Cloud authentication successful for project: {project}")
        
        # Test Sheets API
        sheets_service = build('sheets', 'v4', credentials=credentials)
        print("✅ Google Sheets API client created")
        
        # Test Drive API
        drive_service = build('drive', 'v3', credentials=credentials)
        print("✅ Google Drive API client created")
        
        # Test access to our test spreadsheet
        SPREADSHEET_ID = "1BBhFEyhv0f3IJl4UC3ou5J9dxvN32wcSCjEASAsP5sM"
        
        spreadsheet = sheets_service.spreadsheets().get(spreadsheetId=SPREADSHEET_ID).execute()
        title = spreadsheet.get('properties', {}).get('title', 'Unknown')
        print(f"✅ Successfully accessed test spreadsheet: {title}")
        
        return True
        
    except Exception as error:
        print(f"❌ Error testing Google APIs: {error}")
        return False

def test_claude_desktop_config():
    """Test Claude Desktop configuration"""
    print("\n🖥️ Testing Claude Desktop configuration...")
    
    config_path = os.path.expanduser("~/Library/Application Support/Claude/claude_desktop_config.json")
    
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            if 'mcpServers' in config and 'google-sheets' in config['mcpServers']:
                print("✅ Claude Desktop configuration found")
                
                server_config = config['mcpServers']['google-sheets']
                print(f"📋 Command: {server_config.get('command', 'Not set')}")
                print(f"📋 Args: {server_config.get('args', 'Not set')}")
                
                # Check if the uvx path exists
                command_path = server_config.get('command', '')
                if os.path.exists(command_path):
                    print(f"✅ Command path exists: {command_path}")
                else:
                    print(f"⚠️  Command path may not exist: {command_path}")
                
                return True
            else:
                print("❌ Google Sheets MCP server not configured in Claude Desktop")
                return False
        else:
            print("❌ Claude Desktop configuration file not found")
            print(f"Expected location: {config_path}")
            return False
            
    except Exception as error:
        print(f"❌ Error checking Claude Desktop config: {error}")
        return False

def main():
    """Run all tests"""
    print("🚀 MCP Google Sheets Integration - Comprehensive Test Suite")
    print("=" * 70)
    
    tests = [
        ("uvx Installation", test_uvx_installation),
        ("Google APIs Access", test_google_apis),
        ("MCP Server Standalone", test_mcp_server_standalone),
        ("Claude Desktop Config", test_claude_desktop_config),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 TEST SUMMARY")
    print(f"{'='*70}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MCP Google Sheets integration is ready!")
        print("\n📋 Next steps:")
        print("1. Open Claude Desktop")
        print("2. The MCP server should automatically connect")
        print("3. Try asking: 'List my spreadsheets' or 'Show me data from my test spreadsheet'")
        print("4. For Gemini integration, set GEMINI_API_KEY and run the Gemini integration script")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
