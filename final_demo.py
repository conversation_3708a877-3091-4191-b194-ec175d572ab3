#!/usr/bin/env python3
"""
Final demonstration of the complete MCP Google Sheets + Gemini integration
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🚀 {title}")
    print(f"{'='*60}")

def print_section(title):
    """Print a formatted section header"""
    print(f"\n{'─'*40}")
    print(f"📋 {title}")
    print(f"{'─'*40}")

def demonstrate_setup():
    """Demonstrate the complete setup"""
    print_header("MCP Google Sheets + Gemini Integration - Final Demo")
    
    print("This demonstration shows the complete setup and integration of:")
    print("• MCP Google Sheets server with uvx")
    print("• Google Cloud Application Default Credentials")
    print("• Claude Desktop configuration")
    print("• Gemini AI integration for natural language queries")
    print("• End-to-end testing and validation")

def show_authentication_status():
    """Show current authentication status"""
    print_section("Authentication Status")
    
    try:
        from google.auth import default
        credentials, project = default()
        print(f"✅ Google Cloud Authentication: Active")
        print(f"📋 Project: {project}")
        print(f"📋 Credentials Type: Application Default Credentials (ADC)")
        
        # Check scopes
        if hasattr(credentials, 'scopes') and credentials.scopes:
            print(f"📋 Scopes: {', '.join(credentials.scopes)}")
        else:
            print(f"📋 Scopes: Default (cloud-platform)")
            
    except Exception as e:
        print(f"❌ Authentication Error: {e}")

def show_test_spreadsheet():
    """Show information about the test spreadsheet"""
    print_section("Test Spreadsheet")
    
    SPREADSHEET_ID = "1BBhFEyhv0f3IJl4UC3ou5J9dxvN32wcSCjEASAsP5sM"
    
    try:
        from google.auth import default
        from googleapiclient.discovery import build
        
        credentials, _ = default(scopes=[
            'https://www.googleapis.com/auth/spreadsheets.readonly'
        ])
        
        service = build('sheets', 'v4', credentials=credentials)
        spreadsheet = service.spreadsheets().get(spreadsheetId=SPREADSHEET_ID).execute()
        
        title = spreadsheet.get('properties', {}).get('title', 'Unknown')
        sheets = spreadsheet.get('sheets', [])
        
        print(f"✅ Spreadsheet Access: Working")
        print(f"📋 Title: {title}")
        print(f"📋 ID: {SPREADSHEET_ID}")
        print(f"📋 Sheets: {len(sheets)}")
        
        # Get sample data
        if sheets:
            sheet_name = sheets[0].get('properties', {}).get('title', 'Sheet1')
            result = service.spreadsheets().values().get(
                spreadsheetId=SPREADSHEET_ID,
                range=f"{sheet_name}!A1:E3"
            ).execute()
            
            values = result.get('values', [])
            print(f"📋 Sample Data (first 3 rows):")
            for i, row in enumerate(values):
                print(f"   Row {i+1}: {row}")
                
    except Exception as e:
        print(f"❌ Spreadsheet Access Error: {e}")

def show_mcp_server_status():
    """Show MCP server status"""
    print_section("MCP Server Status")
    
    try:
        # Test uvx availability
        result = subprocess.run(["uvx", "--version"], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ uvx: Available ({result.stdout.strip()})")
        else:
            print(f"❌ uvx: Not available")
            return
        
        # Test MCP package
        print("📦 Testing MCP Google Sheets package...")
        process = subprocess.Popen(
            ["uvx", "mcp-google-sheets@latest"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Give it a moment to start
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ MCP Server: Can start successfully")
            process.terminate()
            process.wait()
        else:
            stdout, stderr = process.communicate()
            print(f"❌ MCP Server: Failed to start")
            if stderr:
                print(f"   Error: {stderr[:200]}...")
                
    except Exception as e:
        print(f"❌ MCP Server Test Error: {e}")

def show_claude_desktop_config():
    """Show Claude Desktop configuration"""
    print_section("Claude Desktop Configuration")
    
    config_path = Path.home() / "Library/Application Support/Claude/claude_desktop_config.json"
    
    if config_path.exists():
        print(f"✅ Configuration File: Found")
        print(f"📋 Location: {config_path}")
        
        try:
            import json
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            if 'mcpServers' in config and 'google-sheets' in config['mcpServers']:
                server_config = config['mcpServers']['google-sheets']
                print(f"✅ Google Sheets MCP Server: Configured")
                print(f"📋 Command: {server_config.get('command')}")
                print(f"📋 Args: {server_config.get('args')}")
            else:
                print(f"❌ Google Sheets MCP Server: Not configured")
                
        except Exception as e:
            print(f"❌ Configuration Error: {e}")
    else:
        print(f"❌ Configuration File: Not found")
        print(f"📋 Expected location: {config_path}")

def show_gemini_integration():
    """Show Gemini integration status"""
    print_section("Gemini Integration")
    
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    if gemini_key:
        print(f"✅ Gemini API Key: Set")
        print(f"📋 Key length: {len(gemini_key)} characters")
        
        # Test Gemini import
        try:
            import google.generativeai as genai
            print(f"✅ Gemini SDK: Available")
            
            # Test configuration (without making API calls)
            genai.configure(api_key=gemini_key)
            print(f"✅ Gemini Configuration: Valid")
            
        except Exception as e:
            print(f"❌ Gemini SDK Error: {e}")
    else:
        print(f"⚠️  Gemini API Key: Not set")
        print(f"📋 Set with: export GEMINI_API_KEY='your-key'")
        print(f"📋 Get key from: https://makersuite.google.com/app/apikey")

def show_usage_examples():
    """Show usage examples"""
    print_section("Usage Examples")
    
    print("🔧 MCP Server (via Claude Desktop):")
    print("   • 'List my spreadsheets'")
    print("   • 'Show me data from my test spreadsheet'")
    print("   • 'Create a new spreadsheet called Project Planning'")
    print("   • 'Add data to cell A1 in my spreadsheet'")
    
    print("\n🤖 Gemini Integration (standalone):")
    print("   • 'What is the average salary in the Engineering department?'")
    print("   • 'How many employees are in each city?'")
    print("   • 'Who is the youngest employee?'")
    print("   • 'Show me salary trends by department'")
    
    print("\n📁 Project Structure:")
    print("   • /gsheetmcp/")
    print("     ├── claude_desktop_config.json")
    print("     ├── test_sheets_access.py")
    print("     ├── create_test_sheet.py")
    print("     ├── test_mcp_integration.py")
    print("     └── gemini-sheets-integration/")
    print("         ├── main.py")
    print("         ├── test_sheets_only.py")
    print("         └── README.md")

def show_next_steps():
    """Show next steps"""
    print_section("Next Steps")
    
    print("1. 🖥️  Open Claude Desktop")
    print("   The MCP server should automatically connect")
    
    print("\n2. 🧪 Test MCP functionality:")
    print("   Ask Claude: 'What MCP servers are available?'")
    print("   Ask Claude: 'List my Google spreadsheets'")
    
    print("\n3. 🤖 Test Gemini integration:")
    print("   cd gemini-sheets-integration")
    print("   export GEMINI_API_KEY='your-key'")
    print("   uv run main.py")
    
    print("\n4. 📊 Explore advanced features:")
    print("   • Create new spreadsheets via Claude")
    print("   • Analyze data with natural language queries")
    print("   • Combine MCP operations with Gemini insights")

def main():
    """Main demonstration function"""
    demonstrate_setup()
    show_authentication_status()
    show_test_spreadsheet()
    show_mcp_server_status()
    show_claude_desktop_config()
    show_gemini_integration()
    show_usage_examples()
    show_next_steps()
    
    print_header("Setup Complete! 🎉")
    print("Your MCP Google Sheets + Gemini integration is ready to use!")
    print("Check the sections above for any issues that need attention.")

if __name__ == "__main__":
    main()
